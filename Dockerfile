# Multi-stage build for better optimization
FROM node:18-alpine AS frontend-builder

# Install dependencies for building
RUN apk add --no-cache python3 make g++

# Build frontend
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm install --legacy-peer-deps

COPY frontend/ ./
RUN npm run build

# Verify build
RUN ls -la dist/ && echo "Frontend build contents:" && find dist/ -type f

# Production stage
FROM node:18-alpine
RUN apk add --no-cache postgresql-client curl python3 make g++

WORKDIR /app

# Copy built frontend from builder stage
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist

# Install backend dependencies
WORKDIR /app/backend
COPY backend/package*.json ./
RUN npm install --legacy-peer-deps --only=production

# Copy backend source
COPY backend/ ./

# Back to app root
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && adduser -S nodejs -u 1001
RUN mkdir -p /app/uploads /app/logs && chown -R nodejs:nodejs /app/uploads /app/logs

USER nodejs
EXPOSE 8081

CMD ["node", "backend/src/server.js"]
