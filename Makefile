dev:
	cd backend && npm run dev

# Development commands
frontend:
	cd frontend && npm run dev

backend:
	cd backend && npm run server

install:
	cd backend && npm install && cd ../frontend && npm install

clean:
	cd backend && rm -rf node_modules && cd ../frontend && rm -rf node_modules

amka:
	sudo pg_ctlcluster 10 main start
restart:
	sudo service postgresql restart
migrate:
	npx sequelize-cli db:migrate
pm2:
	pm2 start ecosystem.config.js
kill:
	sudo kill -9 `sudo lsof -t -i:8081`
serve:
	npm run server
redis:
	sudo service redis-server start
push:
	git push origin master
build:
	cd frontend && npm run build && cd ..
buildess:
	cd frontend && npm run buildess && cd ..
historia:
	git pull origin main --allow-unrelated-histories
undo:
	npx sequelize-cli db:migrate:undo
undo_all:
	npx sequelize-cli db:migrate:undo:all

# migrations:
#       npx sequelize migration:generate --name add-timestamps-to-existing-tables
_test:
	dropdb insertdbName && createdb insertdbName && npm run test
format:
	npx prettier --write .
all:
	make build && make migrate && make deploy
deploy:
	rsync -avz -e "ssh -i /home/<USER>/server/plcke/platinumKe" --exclude="playground" --exclude="node_modules" --exclude=".git" --exclude=".env" --exclude="playground"  /home/<USER>/Platcorp-Projects/automated-email-depositaccounts/ ubuntu@3.71.216.197:/var/www/Depositaging/html
send:
	make build && make deploy


